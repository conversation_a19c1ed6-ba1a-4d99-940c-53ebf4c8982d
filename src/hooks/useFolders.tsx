import { useQuery } from "@tanstack/react-query";
import { getFolders, getPagesByFolder, Folder } from "@/db/draw";
import { useAuth } from "./useAuth";

export function useFolders() {
  const { data: user } = useAuth();

  const {
    data: folders,
    isLoading,
    error,
    refetch: refetchFolders,
  } = useQuery({
    queryKey: ["folders", user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const response = await getFolders(user.id);
      return response.data || [];
    },
    enabled: !!user?.id,
  });

  return {
    folders: folders as Folder[] | undefined,
    isLoading,
    error,
    refetchFolders,
  };
}

export function useFolderPages(folderId: string | null) {
  const { data: user } = useAuth();

  const {
    data: pages,
    isLoading,
    error,
    refetch: refetchPages,
  } = useQuery({
    queryKey: ["folderPages", user?.id, folderId],
    queryFn: async () => {
      if (!user?.id || !folderId) return [];
      const response = await getPagesByFolder(user.id, folderId);
      return response.data || [];
    },
    enabled: !!user?.id && !!folderId,
  });

  return {
    pages,
    isLoading,
    error,
    refetchPages,
  };
}
