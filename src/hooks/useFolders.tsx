import { useQuery } from "@tanstack/react-query";
import { getFolders, getPagesByFolder, Folder } from "@/db/draw";
import { useAuth } from "./useAuth";

export function useFolders() {
  const { user, loading: authLoading } = useAuth();

  console.log("useFolders - user:", user);
  console.log("useFolders - authLoading:", authLoading);

  const {
    data: folders,
    isLoading,
    error,
    refetch: refetchFolders,
  } = useQuery({
    queryKey: ["folders", user?.id],
    queryFn: async () => {
      if (!user?.id) {
        console.log("useFolders - No user ID");
        return [];
      }
      console.log("useFolders - Fetching folders for user:", user.id);
      const response = await getFolders(user.id);
      console.log("useFolders - Response:", response);
      return response.data || [];
    },
    enabled: !!user?.id,
  });

  console.log("useFolders - folders:", folders);
  console.log("useFolders - isLoading:", isLoading);
  console.log("useFolders - error:", error);

  return {
    folders: folders as Folder[] | undefined,
    isLoading,
    error,
    refetchFolders,
  };
}

export function useFolderPages(folderId: string | null) {
  const { data: user } = useAuth();

  const {
    data: pages,
    isLoading,
    error,
    refetch: refetchPages,
  } = useQuery({
    queryKey: ["folderPages", user?.id, folderId],
    queryFn: async () => {
      if (!user?.id || !folderId) return [];
      const response = await getPagesByFolder(user.id, folderId);
      return response.data || [];
    },
    enabled: !!user?.id && !!folderId,
  });

  return {
    pages,
    isLoading,
    error,
    refetchPages,
  };
}
