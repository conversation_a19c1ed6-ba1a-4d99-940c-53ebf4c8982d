import React, { createContext, useContext, useState, useEffect } from "react";
import { useFolders } from "@/hooks/useFolders";

interface FolderContextType {
  selectedFolderId: string | null;
  setSelectedFolderId: (id: string | null) => void;
  folders: any[] | undefined;
  isLoading: boolean;
}

const FolderContext = createContext<FolderContextType | undefined>(undefined);

export function FolderProvider({ children }: { children: React.ReactNode }) {
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const { folders, isLoading, error } = useFolders();

  // Set default selected folder when folders load
  useEffect(() => {
    if (folders && folders.length > 0 && !selectedFolderId) {
      setSelectedFolderId(folders[0].folder_id);
    }
  }, [folders, selectedFolderId]);

  return (
    <FolderContext.Provider
      value={{
        selectedFolderId,
        setSelectedFolderId,
        folders,
        isLoading,
      }}
    >
      {children}
    </FolderContext.Provider>
  );
}

export function useFolderContext() {
  const context = useContext(FolderContext);
  if (context === undefined) {
    throw new Error("useFolderContext must be used within a FolderProvider");
  }
  return context;
}
