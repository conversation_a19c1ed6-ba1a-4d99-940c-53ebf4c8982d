import React, { createContext, useContext, useState, useEffect } from "react";
import { useFolders } from "@/hooks/useFolders";

interface FolderContextType {
  selectedFolderId: string | null;
  setSelectedFolderId: (id: string | null) => void;
  folders: any[] | undefined;
  isLoading: boolean;
}

const FolderContext = createContext<FolderContextType | undefined>(undefined);

export function FolderProvider({ children }: { children: React.ReactNode }) {
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const { folders, isLoading, error } = useFolders();

  // Debug logging
  useEffect(() => {
    console.log("FolderProvider - folders:", folders);
    console.log("FolderProvider - isLoading:", isLoading);
    console.log("FolderProvider - error:", error);
    console.log("FolderProvider - folders type:", typeof folders);
    console.log("FolderProvider - folders length:", folders?.length);
  }, [folders, isLoading, error]);

  // Set default selected folder when folders load
  useEffect(() => {
    if (folders && folders.length > 0 && !selectedFolderId) {
      console.log("Setting default selected folder:", folders[0].folder_id);
      setSelectedFolderId(folders[0].folder_id);
    }
  }, [folders, selectedFolderId]);

  return (
    <FolderContext.Provider
      value={{
        selectedFolderId,
        setSelectedFolderId,
        folders,
        isLoading,
      }}
    >
      {children}
    </FolderContext.Provider>
  );
}

export function useFolderContext() {
  const context = useContext(FolderContext);
  if (context === undefined) {
    throw new Error("useFolderContext must be used within a FolderProvider");
  }
  return context;
}
