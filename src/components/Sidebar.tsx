import { usePages, PageData } from "@/hooks/usePages";
import { Link, useLocation, useNavigate } from "@tanstack/react-router";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import Loader from "./Loader";
import {
  FileText,
  Plus,
  Search,
  Palette
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import ProfileDropdown from "./ProfileDropdown";
import { SearchCommand } from "./SearchCommand";
import { useState } from "react";
import { createNewPage } from "@/db/draw";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

interface SidebarProps {
  className?: string;
}

interface SidebarItemProps {
  page: PageData;
  isActive: boolean;
}

// Removed sections and routes - implementing new structure

function SearchButton({ onClick }: { onClick: () => void }) {
  return (
    <button
      onClick={onClick}
      className="flex items-center justify-between w-full px-3 py-2 text-sm rounded-md transition-all duration-200 ease-in-out cursor-pointer text-text-secondary hover:bg-background-hover hover:text-text-primary"
    >
      <div className="flex items-center gap-3">
        <Search className="h-4 w-4 flex-shrink-0" />
        <span className="font-medium">Search</span>
      </div>
      <div className="flex items-center gap-1">
        <kbd className="kbd">⌘</kbd>
        <kbd className="kbd">K</kbd>
      </div>
    </button>
  );
}

function NewPageDropdown() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  async function createPage() {
    const data = await createNewPage();

    if (data.data && data.data[0]?.page_id) {
      queryClient.invalidateQueries({ queryKey: ["pages"] });
      navigate({ to: "/page/$id", params: { id: data.data[0].page_id } });
      toast("Successfully created a new page!");
    }

    if (data.error) {
      toast("An error occurred", {
        description: `Error: ${data.error.message}`,
      });
    }
  }

  async function createMermaidPage() {
    navigate({ to: "/mermaid" });
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          size="sm"
          variant="ghost"
          className="h-7 w-7 p-0"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={createPage}>Plain Page</DropdownMenuItem>
        <DropdownMenuItem onClick={createMermaidPage}>
          Mermaid Syntax Diagram
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function PageItem({ page, isActive }: SidebarItemProps) {
  return (
    <Link
      to="/page/$id"
      params={{ id: page.page_id }}
      className={cn(
        "group relative flex items-center gap-3 px-3 py-1.5 text-sm rounded-md transition-all duration-200 ease-in-out cursor-pointer",
        isActive
          ? "bg-background-hover text-text-primary"
          : "text-text-secondary hover:bg-background-hover hover:text-text-primary"
      )}
    >
      {isActive && (
        <div className="absolute left-0 top-1/2 -translate-y-1/2 w-0.5 h-4 bg-accent-blue rounded-full" />
      )}
      <FileText className="h-4 w-4 flex-shrink-0 opacity-60" />
      <div className="flex-1 min-w-0">
        <div className="truncate font-medium text-sm">
          {page.name || "Untitled"}
        </div>
      </div>
      <div className="text-xs text-text-muted opacity-0 group-hover:opacity-100 transition-opacity">
        {dayjs(page.updated_at).format("MMM DD")}
      </div>
    </Link>
  );
}

function EmptyPagesState() {
  return (
    <div className="px-3 py-2">
      <div className="text-xs text-text-muted">
        No pages yet
      </div>
    </div>
  );
}

export default function Sidebar({ className }: SidebarProps) {
  const { pages, isLoading } = usePages();
  const location = useLocation();
  const [searchOpen, setSearchOpen] = useState(false);

  // Extract page ID from current location pathname
  const currentPageId = location.pathname.startsWith('/page/')
    ? location.pathname.split('/page/')[1]
    : null;

  return (
    <div
      className={cn(
        "flex h-full w-64 flex-col bg-background-main border-r border-border-subtle",
        className
      )}
    >
      {/* Header with Draw Logo */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-border-subtle">
        <Link to="/pages" className="flex items-center">
          <h1 className="font-virgil text-lg font-bold text-text-primary">Draw</h1>
        </Link>
        <NewPageDropdown />
      </div>

      {/* Sidebar Content */}
      <div className="flex-1 overflow-y-auto py-4">
        {/* Search Section */}
        <div className="mb-6 px-3">
          <SearchButton onClick={() => setSearchOpen(true)} />
        </div>

        {/* Pages Section */}
        <div className="mb-6 px-3">
          <div className="space-y-0.5">
            {isLoading ? (
              <div className="py-2">
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-text-muted border-t-transparent" />
                  <span className="text-xs text-text-muted">Loading...</span>
                </div>
              </div>
            ) : pages.length === 0 ? (
              <EmptyPagesState />
            ) : (
              <>
                {pages.slice(0, 8).map((page) => (
                  <PageItem
                    key={page.page_id}
                    page={page}
                    isActive={currentPageId === page.page_id}
                  />
                ))}
                {pages.length > 8 && (
                  <Link to="/pages">
                    <div className="flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-all duration-200 ease-in-out cursor-pointer text-text-muted hover:bg-background-hover hover:text-text-primary">
                      <div className="h-4 w-4 flex-shrink-0" />
                      <span className="text-xs">View all pages ({pages.length})</span>
                    </div>
                  </Link>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="border-t border-border-subtle p-3">
        <div className="flex items-center justify-between gap-2">
          <Link to="/mermaid">
            <Button
              size="sm"
              variant="ghost"
              className="flex items-center gap-2 h-8 px-3"
            >
              <Palette className="h-4 w-4" />
              <span className="text-sm">Mermaid</span>
            </Button>
          </Link>
          <ProfileDropdown />
        </div>
      </div>

      {/* Search Command Palette */}
      <SearchCommand open={searchOpen} onOpenChange={setSearchOpen} />
    </div>
  );
}
