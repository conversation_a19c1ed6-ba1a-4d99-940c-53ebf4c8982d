import { usePages, PageData } from "@/hooks/usePages";
import { Link, useLocation } from "@tanstack/react-router";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import Loader from "./Loader";
import {
  FileText,
  Plus,
  ChevronRight,
  ChevronDown,
  Home,
  Palette,
  Command,
  MoreHorizontal
} from "lucide-react";
import { Button } from "@/components/ui/button";
import ProfileDropdown from "./ProfileDropdown";
import { useState } from "react";

interface SidebarProps {
  className?: string;
}

interface SidebarItemProps {
  page: PageData;
  isActive: boolean;
}

const mainRoutes = [
  {
    label: "Pages",
    to: "/pages",
    icon: FileText,
    count: null as number | null,
  },
  {
    label: "Mermaid",
    to: "/mermaid",
    icon: Palette,
    count: null as number | null,
  },
];

const sections = [
  {
    title: "WORKSPACE",
    items: mainRoutes,
  },
];

function SidebarSection({ title, children }: { title: string; children: React.ReactNode }) {
  return (
    <div className="mb-6">
      <div className="px-3 mb-3">
        <h3 className="text-xs font-medium text-text-muted uppercase tracking-wider">
          {title}
        </h3>
      </div>
      <div className="space-y-0.5">
        {children}
      </div>
    </div>
  );
}

function SidebarNavItem({
  label,
  to,
  icon: Icon,
  count,
  isActive
}: {
  label: string;
  to: string;
  icon: any;
  count?: number | null;
  isActive: boolean;
}) {
  return (
    <Link to={to}>
      <div
        className={cn(
          "relative flex items-center justify-between px-3 py-2 text-sm rounded-md transition-all duration-200 ease-in-out cursor-pointer",
          isActive
            ? "bg-background-hover text-text-primary"
            : "text-text-secondary hover:bg-background-hover hover:text-text-primary"
        )}
      >
        {isActive && (
          <div className="absolute left-0 top-1/2 -translate-y-1/2 w-0.5 h-4 bg-accent-blue rounded-full" />
        )}
        <div className="flex items-center gap-3">
          <Icon className="h-4 w-4 flex-shrink-0" />
          <span className="font-medium">{label}</span>
        </div>
        {count !== null && count > 0 && (
          <span className="text-xs text-text-muted bg-background-hover px-2 py-0.5 rounded-full min-w-[20px] text-center">
            {count}
          </span>
        )}
      </div>
    </Link>
  );
}

function PageItem({ page, isActive }: SidebarItemProps) {
  return (
    <Link
      to="/page/$id"
      params={{ id: page.page_id }}
      className={cn(
        "group relative flex items-center gap-3 px-3 py-1.5 text-sm rounded-md transition-all duration-200 ease-in-out cursor-pointer",
        isActive
          ? "bg-background-hover text-text-primary"
          : "text-text-secondary hover:bg-background-hover hover:text-text-primary"
      )}
    >
      {isActive && (
        <div className="absolute left-0 top-1/2 -translate-y-1/2 w-0.5 h-4 bg-accent-blue rounded-full" />
      )}
      <FileText className="h-4 w-4 flex-shrink-0 opacity-60" />
      <div className="flex-1 min-w-0">
        <div className="truncate font-medium text-sm">
          {page.name || "Untitled"}
        </div>
      </div>
      <div className="text-xs text-text-muted opacity-0 group-hover:opacity-100 transition-opacity">
        {dayjs(page.updated_at).format("MMM DD")}
      </div>
    </Link>
  );
}

function EmptyPagesState() {
  return (
    <div className="px-3 py-2">
      <div className="text-xs text-text-muted">
        No pages yet
      </div>
    </div>
  );
}

export default function Sidebar({ className }: SidebarProps) {
  const { pages, isLoading } = usePages();
  const location = useLocation();
  const [pagesExpanded, setPagesExpanded] = useState(true);

  // Extract page ID from current location pathname
  const currentPageId = location.pathname.startsWith('/page/')
    ? location.pathname.split('/page/')[1]
    : null;

  // Update main routes with page count
  const updatedMainRoutes = mainRoutes.map(route => {
    if (route.label === "Pages") {
      return { ...route, count: pages?.length || 0 };
    }
    return route;
  });

  return (
    <div
      className={cn(
        "flex h-full w-64 flex-col bg-background-main border-r border-border-subtle",
        className
      )}
    >
      {/* Header with Draw Logo */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-border-subtle">
        <Link to="/pages" className="flex items-center">
          <h1 className="font-virgil text-lg font-bold text-text-primary">Draw</h1>
        </Link>
        <Link to="/pages">
          <Button
            size="sm"
            variant="ghost"
            className="h-7 w-7 p-0"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </Link>
      </div>

      {/* Sidebar Content */}
      <div className="flex-1 overflow-y-auto py-4">
        {/* Main Navigation Sections */}
        {sections.map((section) => (
          <SidebarSection key={section.title} title={section.title}>
            {section.items.map((item) => (
              <SidebarNavItem
                key={item.to}
                label={item.label}
                to={item.to}
                icon={item.icon}
                count={item.count}
                isActive={location.pathname === item.to}
              />
            ))}
          </SidebarSection>
        ))}

        {/* Pages Section */}
        <div className="mb-6">
          <div className="px-3 mb-3">
            <button
              onClick={() => setPagesExpanded(!pagesExpanded)}
              className="flex items-center gap-2 w-full text-left hover:text-text-primary transition-colors"
            >
              {pagesExpanded ? (
                <ChevronDown className="h-3 w-3 text-text-muted" />
              ) : (
                <ChevronRight className="h-3 w-3 text-text-muted" />
              )}
              <h3 className="text-xs font-medium text-text-muted uppercase tracking-wider">
                Recent Pages
              </h3>
            </button>
          </div>

          {pagesExpanded && (
            <div className="space-y-0.5">
              {isLoading ? (
                <div className="px-3 py-2">
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-text-muted border-t-transparent" />
                    <span className="text-xs text-text-muted">Loading...</span>
                  </div>
                </div>
              ) : pages.length === 0 ? (
                <EmptyPagesState />
              ) : (
                <>
                  {pages.slice(0, 8).map((page) => (
                    <PageItem
                      key={page.page_id}
                      page={page}
                      isActive={currentPageId === page.page_id}
                    />
                  ))}
                  {pages.length > 8 && (
                    <Link to="/pages">
                      <div className="flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-all duration-200 ease-in-out cursor-pointer text-text-muted hover:bg-background-hover hover:text-text-primary">
                        <div className="h-4 w-4 flex-shrink-0" />
                        <span className="text-xs">View all pages ({pages.length})</span>
                      </div>
                    </Link>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Bottom Section */}
      <div className="border-t border-border-subtle p-3">
        <div className="flex items-center justify-between px-3 py-2 text-xs text-text-muted">
          <span>Lazy commands</span>
          <div className="flex items-center gap-1">
            <kbd className="kbd">⌘</kbd>
            <kbd className="kbd">K</kbd>
          </div>
        </div>
        <div className="flex justify-center mt-2">
          <ProfileDropdown />
        </div>
      </div>
    </div>
  );
}
