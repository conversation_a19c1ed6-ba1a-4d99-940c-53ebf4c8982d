import { usePages, PageData } from "@/hooks/usePages";
import { Link, useLocation, useNavigate } from "@tanstack/react-router";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);
import Loader from "./Loader";
import {
  FileText,
  Plus,
  Search,
  Palette,
  ChevronDown,
  ChevronRight,
  LayoutDashboard,
  Lock,
  ArrowUpDown,
  MoreHorizontal,
  User
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import ProfileDropdown from "./ProfileDropdown";
import { SearchCommand } from "./SearchCommand";
import { useState } from "react";
import { createNewPage } from "@/db/draw";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

interface SidebarProps {
  className?: string;
}

interface SidebarItemProps {
  page: PageData;
  isActive: boolean;
}

function UserProfileHeader() {
  return (
    <button className="flex items-center justify-between w-full px-4 py-3 border-b border-border-subtle hover:bg-background-hover transition-colors">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-pink-500 to-purple-600 flex items-center justify-center text-white font-medium text-sm">
          M
        </div>
        <span className="text-sm font-medium text-text-primary">myspace</span>
      </div>
      <ChevronDown className="h-4 w-4 text-text-muted" />
    </button>
  );
}

function SearchButton({ onClick }: { onClick: () => void }) {
  return (
    <button
      onClick={onClick}
      className="flex items-center justify-between w-full mx-4 px-3 py-2.5 text-sm rounded-md transition-all duration-200 ease-in-out cursor-pointer text-text-muted hover:bg-background-hover hover:text-text-primary bg-background-card border border-border-subtle"
    >
      <div className="flex items-center gap-3">
        <Search className="h-4 w-4 flex-shrink-0" />
        <span className="font-normal">Quick search</span>
      </div>
      <div className="flex items-center gap-0.5">
        <kbd className="px-1.5 py-0.5 text-xs bg-background-hover rounded text-text-muted border border-border-subtle">⌘</kbd>
        <kbd className="px-1.5 py-0.5 text-xs bg-background-hover rounded text-text-muted border border-border-subtle">K</kbd>
      </div>
    </button>
  );
}

function DashboardButton() {
  const location = useLocation();
  const isActive = location.pathname === "/pages";

  return (
    <Link to="/pages">
      <div
        className={cn(
          "flex items-center gap-3 mx-4 px-3 py-2.5 text-sm rounded-md transition-all duration-200 ease-in-out cursor-pointer",
          isActive
            ? "bg-background-hover text-text-primary"
            : "text-text-secondary hover:bg-background-hover hover:text-text-primary"
        )}
      >
        <LayoutDashboard className="h-4 w-4 flex-shrink-0" />
        <span className="font-medium">Dashboard</span>
      </div>
    </Link>
  );
}

function PrivateSection({
  children,
  isExpanded,
  onToggle
}: {
  children: React.ReactNode;
  isExpanded: boolean;
  onToggle: () => void;
}) {
  return (
    <div className="mx-4">
      <div className="flex items-center justify-between px-3 py-2 mb-2">
        <button
          onClick={onToggle}
          className="flex items-center gap-2 text-text-primary hover:text-text-primary transition-colors"
        >
          {isExpanded ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
          <Lock className="h-4 w-4" />
          <span className="font-medium text-sm">Private</span>
        </button>
        <div className="flex items-center gap-1">
          <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
            <ArrowUpDown className="h-3 w-3" />
          </Button>
          <NewPageDropdown />
        </div>
      </div>
      {isExpanded && (
        <div className="space-y-1">
          {children}
        </div>
      )}
    </div>
  );
}

function NewPageDropdown() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  async function createPage() {
    const data = await createNewPage();

    if (data.data && data.data[0]?.page_id) {
      queryClient.invalidateQueries({ queryKey: ["pages"] });
      navigate({ to: "/page/$id", params: { id: data.data[0].page_id } });
      toast("Successfully created a new page!");
    }

    if (data.error) {
      toast("An error occurred", {
        description: `Error: ${data.error.message}`,
      });
    }
  }

  async function createMermaidPage() {
    navigate({ to: "/mermaid" });
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          size="sm"
          variant="default"
          className="h-6 w-6 p-0 bg-accent-blue hover:bg-blue-600"
        >
          <Plus className="h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={createPage}>Plain Page</DropdownMenuItem>
        <DropdownMenuItem onClick={createMermaidPage}>
          Mermaid Syntax Diagram
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function PageItem({ page, isActive }: SidebarItemProps) {
  // Generate a random color for the thumbnail
  const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-pink-500', 'bg-yellow-500', 'bg-red-500'];
  const colorIndex = page.page_id.charCodeAt(0) % colors.length;
  const thumbnailColor = colors[colorIndex];

  return (
    <Link
      to="/page/$id"
      params={{ id: page.page_id }}
      className={cn(
        "group relative flex items-start gap-3 px-3 py-3 text-sm rounded-lg transition-all duration-200 ease-in-out cursor-pointer",
        isActive
          ? "bg-accent-blue/10 text-text-primary border border-accent-blue/20"
          : "text-text-secondary hover:bg-background-hover hover:text-text-primary"
      )}
    >
      {/* Thumbnail */}
      <div className={cn("w-10 h-8 rounded-md flex-shrink-0 flex items-center justify-center", thumbnailColor)}>
        <FileText className="h-4 w-4 text-white" />
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="truncate font-medium text-sm mb-1">
          {page.name || "Untitled"}
        </div>
        <div className="text-xs text-text-muted">
          by Kainoa Newton
        </div>
        <div className="text-xs text-text-muted">
          {dayjs(page.updated_at).fromNow()}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
          <MoreHorizontal className="h-3 w-3" />
        </Button>
      </div>
    </Link>
  );
}

function EmptyPagesState() {
  return (
    <div className="px-3 py-2">
      <div className="text-xs text-text-muted">
        No pages yet
      </div>
    </div>
  );
}

export default function Sidebar({ className }: SidebarProps) {
  const { pages, isLoading } = usePages();
  const location = useLocation();
  const [searchOpen, setSearchOpen] = useState(false);
  const [privateExpanded, setPrivateExpanded] = useState(true);

  // Extract page ID from current location pathname
  const currentPageId = location.pathname.startsWith('/page/')
    ? location.pathname.split('/page/')[1]
    : null;

  return (
    <div
      className={cn(
        "flex h-full w-72 flex-col bg-background-main border-r border-border-subtle",
        className
      )}
    >
      {/* User Profile Header */}
      <UserProfileHeader />

      {/* Sidebar Content */}
      <div className="flex-1 overflow-y-auto py-4">
        {/* Search Section */}
        <div className="mb-4">
          <SearchButton onClick={() => setSearchOpen(true)} />
        </div>

        {/* Dashboard Section */}
        <div className="mb-6">
          <DashboardButton />
        </div>

        {/* Private Section */}
        <PrivateSection
          isExpanded={privateExpanded}
          onToggle={() => setPrivateExpanded(!privateExpanded)}
        >
          {isLoading ? (
            <div className="px-3 py-4">
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-text-muted border-t-transparent" />
                <span className="text-xs text-text-muted">Loading...</span>
              </div>
            </div>
          ) : pages.length === 0 ? (
            <EmptyPagesState />
          ) : (
            <>
              {pages.slice(0, 6).map((page) => (
                <PageItem
                  key={page.page_id}
                  page={page}
                  isActive={currentPageId === page.page_id}
                />
              ))}
              {pages.length > 6 && (
                <Link to="/pages">
                  <div className="flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-all duration-200 ease-in-out cursor-pointer text-text-muted hover:bg-background-hover hover:text-text-primary">
                    <div className="h-4 w-4 flex-shrink-0" />
                    <span className="text-xs">View all pages ({pages.length})</span>
                  </div>
                </Link>
              )}
            </>
          )}
        </PrivateSection>
      </div>

      {/* Bottom Section */}
      <div className="border-t border-border-subtle p-4">
        <div className="flex items-center justify-between gap-2">
          <Link to="/mermaid">
            <Button
              size="sm"
              variant="ghost"
              className="flex items-center gap-2 h-8 px-3"
            >
              <Palette className="h-4 w-4" />
              <span className="text-sm">Mermaid</span>
            </Button>
          </Link>
          <ProfileDropdown />
        </div>
      </div>

      {/* Search Command Palette */}
      <SearchCommand open={searchOpen} onOpenChange={setSearchOpen} />
    </div>
  );
}
